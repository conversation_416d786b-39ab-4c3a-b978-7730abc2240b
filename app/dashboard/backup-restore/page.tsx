"use client"

import { useState } from "react"
import { Download, <PERSON>ota<PERSON><PERSON>c<PERSON>, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import AdminLayout from "@/components/admin-layout"

const backupHistory = [
  {
    date: "2023-10-01",
    type: "Auto-Backup",
    status: "Completed",
    size: "1.5 GB",
  },
  {
    date: "2023-09-24",
    type: "Manual",
    status: "Failed",
    size: "1.2 GB",
  },
  {
    date: "2023-09-20",
    type: "Auto-Backup",
    status: "Completed",
    size: "1.3 GB",
  },
]

export default function BackupRestorePage() {
  const [scheduleType, setScheduleType] = useState("daily")

  return (
    <AdminLayout>
      <div className="p-6 space-y-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Schedule Auto-Backup */}
          <Card>
            <CardHeader>
              <CardTitle>Schedule Auto-Backup</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <RadioGroup value={scheduleType} onValueChange={setScheduleType}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="daily" id="daily" />
                  <Label htmlFor="daily">Daily</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="weekly" id="weekly" />
                  <Label htmlFor="weekly">Weekly</Label>
                </div>
              </RadioGroup>
              <Button className="bg-emerald-500 hover:bg-emerald-600">Set Schedule</Button>
            </CardContent>
          </Card>

          {/* Export/Import Data */}
          <Card>
            <CardHeader>
              <CardTitle>Export/Import Data</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-3">
                <Button className="bg-emerald-500 hover:bg-emerald-600">Export as CSV</Button>
                <Button className="bg-emerald-500 hover:bg-emerald-600">Export as JSON</Button>
                <Button className="bg-blue-600 hover:bg-blue-700">Import CSV/JSON</Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Backup History Log */}
        <Card>
          <CardHeader>
            <CardTitle>Backup History Log</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead>Option</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {backupHistory.map((backup, index) => (
                  <TableRow key={index}>
                    <TableCell>{backup.date}</TableCell>
                    <TableCell>{backup.type}</TableCell>
                    <TableCell>
                      <Badge
                        variant={backup.status === "Completed" ? "default" : "destructive"}
                        className={backup.status === "Completed" ? "bg-emerald-500" : "bg-red-500"}
                      >
                        {backup.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{backup.size}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button size="sm" className="bg-emerald-500 hover:bg-emerald-600">
                          <Download className="w-4 h-4 mr-1" />
                          Download
                        </Button>
                        <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                          <RotateCcw className="w-4 h-4 mr-1" />
                          Restore
                        </Button>
                        <Button size="sm" variant="destructive">
                          <Trash2 className="w-4 h-4 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Storage Indicator */}
        <Card>
          <CardHeader>
            <CardTitle>Storage Indicator</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-8">
              <div className="relative">
                <div className="w-32 h-32 rounded-full bg-gray-300 relative overflow-hidden">
                  <div
                    className="absolute inset-0 bg-emerald-500"
                    style={{ clipPath: "polygon(50% 50%, 50% 0%, 100% 0%, 100% 20%)" }}
                  ></div>
                  <div
                    className="absolute inset-0 bg-blue-400"
                    style={{ clipPath: "polygon(50% 50%, 100% 20%, 100% 100%, 0% 100%, 0% 0%, 50% 0%)" }}
                  ></div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-emerald-500 rounded"></div>
                  <span className="text-sm">Remaining Storage: 10 GB</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-blue-400 rounded"></div>
                  <span className="text-sm">Used: 90 GB</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
