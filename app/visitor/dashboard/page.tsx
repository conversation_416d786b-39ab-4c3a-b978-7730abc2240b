"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { FileText, User, Clock, CheckCircle, XCircle, ArrowRight, Home, Bell } from "lucide-react"
import { useRouter } from "next/navigation"
import Link from "next/link"

// Mock permit data
const mockPermits = [
  {
    id: "PRM-001",
    title: "Main Facility Access",
    status: "active",
    issueDate: "2024-01-15",
    expiryDate: "2024-01-22",
    qrCode: "QR123456",
    location: "Building A, Floor 2",
  },
  {
    id: "PRM-002",
    title: "Mining Area Visit",
    status: "expired",
    issueDate: "2024-01-10",
    expiryDate: "2024-01-17",
    qrCode: "QR789012",
    location: "Mining Site 1",
  },
  {
    id: "PRM-003",
    title: "Safety Training Area",
    status: "pending",
    issueDate: "2024-01-20",
    expiryDate: "2024-01-27",
    qrCode: "QR345678",
    location: "Training Center",
  },
]

export default function VisitorDashboardPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("permits")
  const [userEmail, setUserEmail] = useState("")

  useEffect(() => {
    // Check if user is logged in
    const isLoggedIn = localStorage.getItem("visitorLoggedIn")
    if (!isLoggedIn) {
      router.replace("/visitor/login")
      return
    }

    const email = localStorage.getItem("visitorEmail")
    if (email) {
      setUserEmail(email)
    }
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem("visitorLoggedIn")
    localStorage.removeItem("visitorEmail")
    router.replace("/visitor/login")
  }

  const handlePermitClick = (permitId: string) => {
    router.push(`/visitor/permit-detail/${permitId}`)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "expired":
        return "bg-red-100 text-red-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="w-4 h-4" />
      case "expired":
        return <XCircle className="w-4 h-4" />
      case "pending":
        return <Clock className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  // Home Tab - Permits content with modern design
  const HomeTab = () => (
    <div className="space-y-6">
      {/* Welcome Banner */}
      <div className="bg-gradient-to-br from-blue-500 to-blue-700 rounded-2xl p-6 text-white relative overflow-hidden">
        <div className="relative z-10">
          <h2 className="text-xl font-bold mb-2">Welcome Back! 👋</h2>
          <h3 className="text-lg font-semibold mb-3">Visitor Management Portal</h3>
          <p className="text-sm mb-4 opacity-90">
            Manage your permits and access requests easily through our digital platform
          </p>
          <Link href="/visitor/register">
            <Button className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white border border-white border-opacity-30 rounded-full px-6 py-2 text-sm font-medium">
              Apply New Permit
            </Button>
          </Link>
        </div>
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
        <div className="absolute bottom-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full translate-y-4 translate-x-4"></div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-white rounded-xl p-4 text-center shadow-sm border border-gray-100">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
            <FileText className="w-5 h-5 text-blue-600" />
          </div>
          <p className="text-lg font-bold text-gray-800">{mockPermits.length}</p>
          <p className="text-xs text-gray-600">Total</p>
        </div>
        <div className="bg-white rounded-xl p-4 text-center shadow-sm border border-gray-100">
          <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
          </div>
          <p className="text-lg font-bold text-green-600">
            {mockPermits.filter((p) => p.status === "active").length}
          </p>
          <p className="text-xs text-gray-600">Active</p>
        </div>
        <div className="bg-white rounded-xl p-4 text-center shadow-sm border border-gray-100">
          <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2">
            <Clock className="w-5 h-5 text-orange-600" />
          </div>
          <p className="text-lg font-bold text-orange-600">
            {mockPermits.filter((p) => p.status === "pending").length}
          </p>
          <p className="text-xs text-gray-600">Pending</p>
        </div>
      </div>

      {/* Recent Permits */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-gray-800">Recent Permits</h3>
          <button
            onClick={() => setActiveTab("permits-list")}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium"
          >
            View All
          </button>
        </div>

        <div className="space-y-3">
          {mockPermits.slice(0, 2).map((permit) => (
            <Card
              key={permit.id}
              className="shadow-sm border border-gray-100 cursor-pointer hover:shadow-md transition-shadow rounded-2xl"
              onClick={() => handlePermitClick(permit.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-800 mb-1">{permit.title}</h4>
                    <p className="text-sm text-gray-600 mb-2">{permit.location}</p>
                    <Badge className={`${getStatusColor(permit.status)} border-0 rounded-full text-xs`}>
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(permit.status)}
                        <span className="capitalize">{permit.status}</span>
                      </div>
                    </Badge>
                  </div>
                  <ArrowRight className="w-5 h-5 text-gray-400" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )

  const PermitsTab = () => (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-800">My Permits</h2>
        <Link href="/visitor/register">
          <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6">New Permit</Button>
        </Link>
      </div>

      {/* Permits List */}
      <div className="space-y-4">
        {mockPermits.map((permit) => (
          <Card
            key={permit.id}
            className="shadow-sm border border-gray-100 cursor-pointer hover:shadow-md transition-shadow rounded-2xl"
            onClick={() => handlePermitClick(permit.id)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800 mb-1">{permit.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">{permit.location}</p>
                  <div className="flex items-center space-x-2">
                    <Badge className={`${getStatusColor(permit.status)} border-0 rounded-full`}>
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(permit.status)}
                        <span className="capitalize">{permit.status}</span>
                      </div>
                    </Badge>
                    <span className="text-xs text-gray-500">ID: {permit.id}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="text-right text-sm text-gray-500">
                    <p>Expires</p>
                    <p className="font-medium">{permit.expiryDate}</p>
                  </div>
                  <ArrowRight className="w-5 h-5 text-gray-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )

  const ProfileTab = () => (
    <div className="space-y-6">
      {/* User Info */}
      <Card className="shadow-sm border border-gray-100 rounded-2xl">
        <CardContent className="p-6">
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
              <User className="w-8 h-8 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">John Doe</h3>
              <p className="text-gray-600">{userEmail}</p>
              <p className="text-xs text-gray-500">Member since January 2024</p>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="bg-gray-50 rounded-xl p-3">
              <p className="text-lg font-bold text-gray-800">{mockPermits.length}</p>
              <p className="text-xs text-gray-600">Total Permits</p>
            </div>
            <div className="bg-green-50 rounded-xl p-3">
              <p className="text-lg font-bold text-green-600">
                {mockPermits.filter((p) => p.status === "active").length}
              </p>
              <p className="text-xs text-gray-600">Active</p>
            </div>
            <div className="bg-orange-50 rounded-xl p-3">
              <p className="text-lg font-bold text-orange-600">
                {mockPermits.filter((p) => p.status === "pending").length}
              </p>
              <p className="text-xs text-gray-600">Pending</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="space-y-3">
        <button className="w-full bg-white border border-gray-100 rounded-2xl p-4 text-left hover:bg-gray-50 transition-colors shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-blue-600" />
              </div>
              <span className="font-medium text-gray-700">Edit Profile</span>
            </div>
            <ArrowRight className="w-5 h-5 text-gray-400" />
          </div>
        </button>

        <button className="w-full bg-white border border-gray-100 rounded-2xl p-4 text-left hover:bg-gray-50 transition-colors shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <Bell className="w-5 h-5 text-green-600" />
              </div>
              <span className="font-medium text-gray-700">Notification Settings</span>
            </div>
            <ArrowRight className="w-5 h-5 text-gray-400" />
          </div>
        </button>

        <button className="w-full bg-white border border-gray-100 rounded-2xl p-4 text-left hover:bg-gray-50 transition-colors shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <FileText className="w-5 h-5 text-purple-600" />
              </div>
              <span className="font-medium text-gray-700">Privacy Policy</span>
            </div>
            <ArrowRight className="w-5 h-5 text-gray-400" />
          </div>
        </button>
      </div>

      {/* Logout */}
      <Button
        onClick={handleLogout}
        variant="destructive"
        className="w-full h-12 rounded-2xl font-medium bg-red-500 hover:bg-red-600"
      >
        Sign Out
      </Button>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-white shadow-sm px-4 py-4 sticky top-0 z-10">
        <div className="max-w-md mx-auto flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-800">Visitor Portal</h1>
          <div className="flex items-center space-x-3">
            <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors">
              <Bell className="w-5 h-5 text-gray-600" />
            </button>
            <button
              onClick={() => setActiveTab("profile")}
              className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              <User className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-md mx-auto px-4 py-4">
        {activeTab === "permits" && <HomeTab />}
        {activeTab === "permits-list" && <PermitsTab />}
        {activeTab === "profile" && <ProfileTab />}
      </div>

      {/* Bottom Navigation - Simple 2 tabs */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg">
        <div className="max-w-md mx-auto">
          <div className="flex">
            <button
              onClick={() => setActiveTab("permits")}
              className={`flex-1 py-3 px-2 text-center transition-colors ${
                activeTab === "permits" || activeTab === "permits-list"
                  ? "text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <Home className="w-6 h-6 mx-auto mb-1" />
              <span className="text-xs font-medium">Home</span>
            </button>
            <button
              onClick={() => setActiveTab("profile")}
              className={`flex-1 py-3 px-2 text-center transition-colors ${
                activeTab === "profile"
                  ? "text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <User className="w-6 h-6 mx-auto mb-1" />
              <span className="text-xs font-medium">Profile</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
