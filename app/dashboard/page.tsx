"use client"

import { useState } from "react"
import { <PERSON>, <PERSON>r<PERSON><PERSON><PERSON>, <PERSON>r<PERSON>, <PERSON>, Users } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import AdminLayout from "@/components/admin-layout"
import dynamic from "next/dynamic"

// Dynamically import the map component to avoid SSR issues
const MapComponent = dynamic(() => import("@/components/map-component"), {
  ssr: false,
  loading: () => <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">Loading map...</div>,
})

// Static data for the visitor counter chart
const visitorChartData = [
  { name: "<PERSON>", visitors: 65, approved: 45 },
  { name: "Feb", visitors: 59, approved: 42 },
  { name: "Mar", visitors: 80, approved: 65 },
  { name: "Apr", visitors: 81, approved: 70 },
  { name: "May", visitors: 56, approved: 48 },
  { name: "Jun", visitors: 55, approved: 45 },
  { name: "Jul", visitors: 40, approved: 35 },
  { name: "Aug", visitors: 65, approved: 55 },
  { name: "Sep", visitors: 75, approved: 62 },
  { name: "Oct", visitors: 85, approved: 70 },
  { name: "Nov", visitors: 90, approved: 75 },
  { name: "Dec", visitors: 95, approved: 80 },
]

const visitorData = [
  {
    id: 1,
    name: "Riko Susanto Yuda",
    nik: "003/145485/SPP UMKM I",
    email: "<EMAIL>",
    phone: "081169111198",
    timestamp: "2024-10-01 11:47:43",
    status: "Int",
  },
  {
    id: 2,
    name: "Riki",
    nik: "004/145510/SPP UMKM I",
    email: "<EMAIL>",
    phone: "081169111198",
    timestamp: "2024-08-26 12:54:26",
    status: "Approved",
  },
  {
    id: 3,
    name: "Ummi Azizah Zahrah",
    nik: "005/146714/SPP UMKM I",
    email: "<EMAIL>",
    phone: "081169111198",
    timestamp: "2024-08-16 06:02:27",
    status: "Int",
  },
  {
    id: 4,
    name: "Janoko Wiwoho",
    nik: "007/145503/SPP UMKM I",
    email: "<EMAIL>",
    phone: "081169111198",
    timestamp: "2024-08-12 07:07:25",
    status: "Approved",
  },
  {
    id: 5,
    name: "Dimas Gustian Sapana",
    nik: "012/146874/SPP UMKM I",
    email: "<EMAIL>",
    phone: "081169111198",
    timestamp: "2024-08-11 03:24:19",
    status: "Approved",
  },
]

export default function DashboardPage() {
  const [searchTerm, setSearchTerm] = useState("")

  return (
    <AdminLayout>
      <div className="p-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-green-100 rounded-lg">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Registered Visitors</p>
                  <p className="text-3xl font-bold">5</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <UserCheck className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Approved Visitors</p>
                  <p className="text-3xl font-bold">3</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-gray-100 rounded-lg">
                  <UserX className="w-6 h-6 text-gray-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Unapproved Visitors</p>
                  <p className="text-3xl font-bold">2</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-orange-100 rounded-lg">
                  <Clock className="w-6 h-6 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Expired Visitors</p>
                  <p className="text-3xl font-bold">0</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Visitor Counter Chart */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Visitor Counter</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={visitorChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="visitors" stroke="#8884d8" strokeWidth={2} name="Total Visitors" />
                    <Line
                      type="monotone"
                      dataKey="approved"
                      stroke="#10b981"
                      strokeWidth={2}
                      name="Approved Visitors"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Map */}
          <Card>
            <CardContent className="p-6">
              <MapComponent />
            </CardContent>
          </Card>
        </div>

        {/* Recent Visitors Table */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Recent Visitors</CardTitle>
              <div className="flex items-center space-x-4">
                <Button variant="outline" size="sm">
                  Filter by date
                </Button>
                <Button size="sm" className="bg-emerald-500 hover:bg-emerald-600">
                  Apply Filter
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Show</span>
                <Select defaultValue="10">
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-gray-600">entries</span>
              </div>

              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
            </div>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>NO.</TableHead>
                  <TableHead>FULL NAME</TableHead>
                  <TableHead>NIK</TableHead>
                  <TableHead>EMAIL</TableHead>
                  <TableHead>NO. HP</TableHead>
                  <TableHead>TIMESTAMP</TableHead>
                  <TableHead>ACTION</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {visitorData.map((visitor) => (
                  <TableRow key={visitor.id}>
                    <TableCell>{visitor.id}</TableCell>
                    <TableCell>{visitor.name}</TableCell>
                    <TableCell>{visitor.nik}</TableCell>
                    <TableCell>{visitor.email}</TableCell>
                    <TableCell>{visitor.phone}</TableCell>
                    <TableCell>{visitor.timestamp}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant={visitor.status === "Approved" ? "default" : "secondary"}
                          className={visitor.status === "Approved" ? "bg-blue-500" : "bg-gray-500"}
                        >
                          {visitor.status}
                        </Badge>
                        <Button size="sm" variant="outline">
                          <Search className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <div className="flex items-center justify-between mt-4">
              <span className="text-sm text-gray-600">Showing 1 to 5 of 5 entries</span>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" disabled>
                  Previous
                </Button>
                <Button size="sm" className="bg-blue-500">
                  1
                </Button>
                <Button variant="outline" size="sm" disabled>
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notification Panel */}
        <Card>
          <CardHeader>
            <CardTitle>Notification Panel</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <span className="text-sm">Alice Johnson's visit expires in 6 hours</span>
              <Button size="sm">Extend</Button>
            </div>
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <span className="text-sm">Bob Smith's visit expires in 4 hours</span>
              <Button size="sm">Extend</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
