"use client"

import AdminLayout from "@/components/admin-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function VisitorManagementPage() {
  return (
    <AdminLayout>
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Visitor Management</h1>

        <Card>
          <CardHeader>
            <CardTitle>Visitor Management System</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              This page will contain visitor management functionality including visitor registration, approval
              workflows, and visitor tracking features.
            </p>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
