"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import AdminLayout from "@/components/admin-layout"

export default function SystemSettingsPage() {
  const [whatsappNumber, setWhatsappNumber] = useState("+1 ***********")
  const [messageTemplate, setMessageTemplate] = useState("")
  const [quizTime, setQuizTime] = useState("")

  return (
    <AdminLayout>
      <div className="p-6 space-y-8">
        {/* System Settings Header */}
        <div>
          <h1 className="text-2xl font-bold mb-2">System Settings</h1>
          <p className="text-gray-600 mb-4">Manage the system settings for optimal performance and security.</p>
          <Button className="bg-emerald-500 hover:bg-emerald-600">Update Settings</Button>
        </div>

        {/* Content Management */}
        <Card>
          <CardHeader>
            <CardTitle>Content Management</CardTitle>
          </CardHeader>
          <CardContent className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Upload Video */}
              <div className="space-y-4">
                <div>
                  <Label className="text-base font-medium">Upload Video</Label>
                  <p className="text-sm text-gray-600">Max 500MB</p>
                </div>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center bg-gray-50">
                  <p className="text-gray-500">Drag and drop video file here or click to browse</p>
                </div>
              </div>

              {/* Quiz Question Editor */}
              <div className="space-y-4">
                <div>
                  <Label className="text-base font-medium">Quiz Question Editor</Label>
                  <p className="text-sm text-gray-600">Drag-and-drop to rearrange questions.</p>
                </div>
                <div className="border rounded-lg p-4 bg-gray-50 space-y-2">
                  <p className="text-sm">Question 1: What is the safety protocol?</p>
                  <p className="text-sm">Question 2: How to operate machinery?</p>
                </div>
              </div>
            </div>

            {/* Set Quiz Appearance Time */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Set Quiz Appearance Time</Label>
              <Input
                placeholder="Time in seconds"
                value={quizTime}
                onChange={(e) => setQuizTime(e.target.value)}
                className="max-w-md"
              />
            </div>
          </CardContent>
        </Card>

        {/* Notification Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Notification Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Company WhatsApp Number */}
              <div className="space-y-4">
                <Label className="text-base font-medium">Company WhatsApp Number</Label>
                <Input
                  value={whatsappNumber}
                  onChange={(e) => setWhatsappNumber(e.target.value)}
                  placeholder="+1 ***********"
                />
              </div>

              {/* Custom Message Template */}
              <div className="space-y-4">
                <Label className="text-base font-medium">Custom Message Template</Label>
                <Textarea
                  value={messageTemplate}
                  onChange={(e) => setMessageTemplate(e.target.value)}
                  placeholder="Enter your message template here..."
                  rows={4}
                />
              </div>
            </div>

            {/* Test Connection */}
            <div className="flex items-center justify-between pt-4 border-t">
              <p className="text-sm text-gray-600">Test the connection with the provided details.</p>
              <Button className="bg-emerald-500 hover:bg-emerald-600">Test Connection</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
