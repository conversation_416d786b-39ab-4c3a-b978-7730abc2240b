"use client"

import { useEffect, useRef, useState } from "react"
import { MapPin, Users, Clock } from "lucide-react"

// Static visitor location data
const visitorLocations = [
  { id: 1, name: "<PERSON><PERSON>", lat: -6.2088, lng: 106.8456, status: "approved" },
  { id: 2, name: "<PERSON><PERSON>", lat: -6.2, lng: 106.83, status: "approved" },
  { id: 3, name: "<PERSON><PERSON>", lat: -6.215, lng: 106.85, status: "pending" },
  { id: 4, name: "<PERSON><PERSON>", lat: -6.195, lng: 106.84, status: "approved" },
  { id: 5, name: "<PERSON><PERSON>", lat: -6.22, lng: 106.86, status: "approved" },
]

export default function MapComponent() {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [mapError, setMapError] = useState(false)

  useEffect(() => {
    if (typeof window !== "undefined" && mapRef.current && !mapInstanceRef.current) {
      // Add timeout to simulate loading and improve UX
      const loadTimeout = setTimeout(() => {
        // Dynamically import Leaflet to avoid SSR issues
        import("leaflet")
          .then((L) => {
            try {
              // Initialize map with better performance settings
              const map = L.map(mapRef.current!, {
                zoomControl: true,
                scrollWheelZoom: false,
                doubleClickZoom: true,
                boxZoom: false,
                keyboard: false,
                dragging: true,
                tap: false,
              }).setView([-6.2088, 106.8456], 12)

              // Add OpenStreetMap tiles with better caching
              L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
                attribution: '© OpenStreetMap',
                maxZoom: 18,
                tileSize: 256,
                zoomOffset: 0,
              }).addTo(map)

              // Add markers for each visitor location with better performance
              visitorLocations.forEach((location) => {
                const markerColor = location.status === "approved" ? "#10b981" : "#f59e0b"

                // Create optimized custom icon
                const customIcon = L.divIcon({
                  className: "custom-marker",
                  html: `<div style="
                    background-color: ${markerColor};
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    border: 2px solid white;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
                  "></div>`,
                  iconSize: [16, 16],
                  iconAnchor: [8, 8],
                })

                const marker = L.marker([location.lat, location.lng], { icon: customIcon }).addTo(map)

                // Add popup with visitor info
                marker.bindPopup(`
                  <div style="font-size: 12px; min-width: 120px;">
                    <strong>${location.name}</strong><br/>
                    <span style="color: ${markerColor}; font-weight: bold;">
                      ${location.status.charAt(0).toUpperCase() + location.status.slice(1)}
                    </span>
                  </div>
                `)
              })

              mapInstanceRef.current = map
              setIsLoading(false)
              setMapError(false)
            } catch (error) {
              console.error("Map initialization error:", error)
              setMapError(true)
              setIsLoading(false)
            }
          })
          .catch((error) => {
            console.error("Failed to load map:", error)
            setMapError(true)
            setIsLoading(false)
          })
      }, 500) // Small delay to improve perceived performance

      return () => {
        clearTimeout(loadTimeout)
        if (mapInstanceRef.current) {
          mapInstanceRef.current.remove()
          mapInstanceRef.current = null
        }
      }
    }

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [])

  // Fallback component when map fails to load
  if (mapError) {
    return (
      <div className="h-64 w-full rounded-lg overflow-hidden border border-gray-200 bg-gray-50">
        <div className="h-full w-full flex flex-col items-center justify-center p-4">
          <MapPin className="w-8 h-8 text-gray-400 mb-2" />
          <h3 className="font-medium text-gray-600 mb-1">Visitor Locations</h3>
          <p className="text-sm text-gray-500 text-center mb-4">Map temporarily unavailable</p>

          {/* Simple stats as fallback */}
          <div className="grid grid-cols-2 gap-4 w-full max-w-xs">
            <div className="bg-white rounded-lg p-3 text-center shadow-sm">
              <Users className="w-5 h-5 text-green-600 mx-auto mb-1" />
              <p className="text-sm font-medium text-gray-700">
                {visitorLocations.filter(v => v.status === "approved").length}
              </p>
              <p className="text-xs text-gray-500">Approved</p>
            </div>
            <div className="bg-white rounded-lg p-3 text-center shadow-sm">
              <Clock className="w-5 h-5 text-orange-600 mx-auto mb-1" />
              <p className="text-sm font-medium text-gray-700">
                {visitorLocations.filter(v => v.status === "pending").length}
              </p>
              <p className="text-xs text-gray-500">Pending</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-64 w-full rounded-lg overflow-hidden border border-gray-200 relative">
      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-50 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Loading map...</p>
          </div>
        </div>
      )}

      {/* Map container */}
      <div ref={mapRef} className="h-full w-full" />

      {/* Map title overlay */}
      <div className="absolute top-2 left-2 bg-white bg-opacity-90 rounded-lg px-3 py-1 shadow-sm">
        <p className="text-xs font-medium text-gray-700">Visitor Locations</p>
      </div>
    </div>
  )
}
