"use client"

import { useState } from "react"
import { <PERSON>, FileDown, FileText, <PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import AdminLayout from "@/components/admin-layout"

const logsData = [
  {
    name: "admin",
    email: "<EMAIL>",
    role: "Admin",
    ipAddress: "*************",
    loginDateTime: "02-04-2023 03:46:54",
  },
  {
    name: "admin",
    email: "<EMAIL>",
    role: "Admin",
    ipAddress: "**************",
    loginDateTime: "02-04-2023 03:33:54",
  },
  {
    name: "dr. <PERSON>",
    email: "<EMAIL>",
    role: "Doctor",
    ipAddress: "*************",
    loginDateTime: "02-04-2023 03:29:28",
  },
  {
    name: "admin",
    email: "<EMAIL>",
    role: "Admin",
    ipAddress: "*************",
    loginDateTime: "02-04-2023 03:28:54",
  },
  {
    name: "dr. Lia",
    email: "<EMAIL>",
    role: "Doctor",
    ipAddress: "*************",
    loginDateTime: "02-04-2023 00:22:33",
  },
  {
    name: "admin",
    email: "<EMAIL>",
    role: "Admin",
    ipAddress: "*************",
    loginDateTime: "02-04-2023 00:20:40",
  },
  {
    name: "admin",
    email: "<EMAIL>",
    role: "Admin",
    ipAddress: "*************",
    loginDateTime: "01-04-2023 23:27:40",
  },
  {
    name: "admin",
    email: "<EMAIL>",
    role: "Admin",
    ipAddress: "**************",
    loginDateTime: "01-04-2023 23:09:21",
  },
  {
    name: "dr. Lia",
    email: "<EMAIL>",
    role: "Doctor",
    ipAddress: "**************",
    loginDateTime: "01-04-2023 23:08:29",
  },
  {
    name: "Mr Receptionist",
    email: "<EMAIL>",
    role: "Receptionist",
    ipAddress: "***************",
    loginDateTime: "01-04-2023 23:00:20",
  },
]

export default function LogsPage() {
  const [searchTerm, setSearchTerm] = useState("")

  return (
    <AdminLayout>
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Logs</h1>

        {/* Controls */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <Select defaultValue="10">
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-4">
            {/* Export Buttons */}
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Copy className="w-4 h-4 mr-1" />
                Copy
              </Button>
              <Button variant="outline" size="sm">
                <FileText className="w-4 h-4 mr-1" />
                Excel
              </Button>
              <Button variant="outline" size="sm">
                <FileDown className="w-4 h-4 mr-1" />
                CSV
              </Button>
              <Button variant="outline" size="sm">
                <FileText className="w-4 h-4 mr-1" />
                PDF
              </Button>
              <Button variant="outline" size="sm">
                <Printer className="w-4 h-4 mr-1" />
                Print
              </Button>
            </div>

            {/* Search */}
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
          </div>
        </div>

        {/* Logs Table */}
        <div className="bg-white rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="cursor-pointer hover:bg-gray-50">Name ↕</TableHead>
                <TableHead className="cursor-pointer hover:bg-gray-50">Email ↕</TableHead>
                <TableHead>Role</TableHead>
                <TableHead className="cursor-pointer hover:bg-gray-50">IP Address ↕</TableHead>
                <TableHead>Login Date Time</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {logsData.map((log, index) => (
                <TableRow key={index} className="hover:bg-gray-50">
                  <TableCell className="text-blue-600 hover:underline cursor-pointer">{log.name}</TableCell>
                  <TableCell className="text-blue-600 hover:underline cursor-pointer">{log.email}</TableCell>
                  <TableCell>{log.role}</TableCell>
                  <TableCell>{log.ipAddress}</TableCell>
                  <TableCell>{log.loginDateTime}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-4">
          <span className="text-sm text-gray-600">Showing 1 to 10 of 10 entries</span>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" disabled>
              Previous
            </Button>
            <Button size="sm" className="bg-blue-500">
              1
            </Button>
            <Button variant="outline" size="sm" disabled>
              Next
            </Button>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
