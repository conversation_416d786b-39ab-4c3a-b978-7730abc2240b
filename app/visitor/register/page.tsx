"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { MapPin, Upload, Camera, ArrowLeft, CheckCircle } from "lucide-react"
import { useRouter } from "next/navigation"
import Link from "next/link"

export default function VisitorRegister() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    fullName: "",
    username: "",
    password: "",
    phone: "",
    email: "",
    visitDuration: "",
    idCard: null as File | null,
    selfie: null as File | null,
    location: null as { lat: number; lng: number } | null,
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleFileUpload = (field: string, file: File | null) => {
    setFormData((prev) => ({ ...prev, [field]: file }))
  }

  const handleGetLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setFormData((prev) => ({
            ...prev,
            location: {
              lat: position.coords.latitude,
              lng: position.coords.longitude,
            },
          }))
        },
        (error) => {
          console.error("Error getting location:", error)
          alert("Unable to get location. Please enable location services.")
        }
      )
    } else {
      alert("Geolocation is not supported by this browser.")
    }
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))
    
    // Store registration data
    localStorage.setItem("registrationData", JSON.stringify(formData))
    
    setIsLoading(false)
    router.push("/visitor/quiz")
  }

  const isFormValid = () => {
    return (
      formData.fullName &&
      formData.username &&
      formData.password &&
      formData.phone &&
      formData.email &&
      formData.visitDuration &&
      formData.idCard &&
      formData.selfie &&
      formData.location
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Sticky Header */}
      <div className="sticky top-0 z-50 bg-white shadow-sm px-4 py-4">
        <div className="max-w-md mx-auto flex items-center">
          <Link href="/visitor/login">
            <Button variant="ghost" size="icon" className="mr-3 rounded-full">
              <ArrowLeft className="w-5 h-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-xl font-bold text-gray-800">Visitor Registration</h1>
            <p className="text-sm text-gray-600">Create your visitor account</p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 px-4 py-6">
        <div className="max-w-md mx-auto">
          {/* Company Logo Section */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-blue-200 mb-6 text-center">
            <div className="bg-gray-100 rounded-xl p-8 mb-4">
              <h2 className="text-2xl font-bold text-gray-800">COMPANY LOGO</h2>
            </div>
            <h3 className="text-xl font-bold text-gray-800 mb-2">Visitor Registration</h3>
          </div>

          {/* Registration Form */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 space-y-5">
            {/* Full Name */}
            <div>
              <Input
                placeholder="Full Name"
                value={formData.fullName}
                onChange={(e) => handleInputChange("fullName", e.target.value)}
                className="h-12 bg-gray-50 border border-gray-200 rounded-full px-4 text-gray-700 placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
              />
            </div>

            {/* Username */}
            <div>
              <Input
                placeholder="Username"
                value={formData.username}
                onChange={(e) => handleInputChange("username", e.target.value)}
                className="h-12 bg-gray-50 border border-gray-200 rounded-full px-4 text-gray-700 placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
              />
            </div>

            {/* Password */}
            <div>
              <Input
                type="password"
                placeholder="Password"
                value={formData.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                className="h-12 bg-gray-50 border border-gray-200 rounded-full px-4 text-gray-700 placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
              />
            </div>

            {/* Phone Number */}
            <div>
              <Input
                placeholder="Phone Number (WhatsApp)"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                className="h-12 bg-gray-50 border border-gray-200 rounded-full px-4 text-gray-700 placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
              />
            </div>

            {/* Email */}
            <div>
              <Input
                type="email"
                placeholder="Email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className="h-12 bg-gray-50 border border-gray-200 rounded-full px-4 text-gray-700 placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
              />
            </div>

            {/* Upload ID Card */}
            <div className="flex items-center justify-between py-2">
              <span className="text-gray-700 font-medium">Upload ID Card:</span>
              <div className="flex items-center space-x-2">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleFileUpload("idCard", e.target.files?.[0] || null)}
                  className="hidden"
                  id="idCard"
                />
                <label htmlFor="idCard" className="cursor-pointer">
                  <Button type="button" className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6">
                    Upload
                  </Button>
                </label>
                {formData.idCard && (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                )}
              </div>
            </div>

            {/* Take Selfie */}
            <div className="flex items-center justify-between py-2">
              <span className="text-gray-700 font-medium">Take a Selfie:</span>
              <div className="flex items-center space-x-2">
                <input
                  type="file"
                  accept="image/*"
                  capture="user"
                  onChange={(e) => handleFileUpload("selfie", e.target.files?.[0] || null)}
                  className="hidden"
                  id="selfie"
                />
                <label htmlFor="selfie" className="cursor-pointer">
                  <Button type="button" className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6">
                    Capture
                  </Button>
                </label>
                {formData.selfie && (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                )}
              </div>
            </div>

            {/* Visit Duration */}
            <div>
              <Input
                placeholder="Visit Duration (Date and Time)"
                value={formData.visitDuration}
                onChange={(e) => handleInputChange("visitDuration", e.target.value)}
                className="h-12 bg-gray-50 border border-gray-200 rounded-full px-4 text-gray-700 placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white"
              />
            </div>

            {/* Get Location Button */}
            <Button
              onClick={handleGetLocation}
              className="w-full h-12 bg-green-500 hover:bg-green-600 text-white rounded-full font-medium text-base shadow-sm"
            >
              <MapPin className="w-4 h-4 mr-2" />
              Get Location
              {formData.location && <CheckCircle className="w-4 h-4 ml-2" />}
            </Button>

            {/* Submit Button */}
            <Button
              onClick={handleSubmit}
              disabled={!isFormValid() || isLoading}
              className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full font-medium text-base shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Submitting...</span>
                </div>
              ) : (
                "Submit"
              )}
            </Button>
          </div>

          {/* Footer */}
          <div className="mt-6 text-center text-xs text-gray-500">
            <p>© 2023 Nickel Mining Co.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
